import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'firestore.dart';
import 'logging_service.dart';

/// Service for managing Firebase Cloud Messaging (FCM) integration
///
/// This service handles:
/// - FCM token management and storage
/// - Permission requests for notifications
/// - Message handling for foreground, background, and terminated states
/// - Topic subscription and unsubscription
/// - Integration with existing FirestoreService for data persistence
class NotificationService {
  static NotificationService? _instance;
  static const String _logTag = 'NotificationService';

  bool _isInitialized = false;
  String? _currentToken;
  final StreamController<RemoteMessage> _messageController =
      StreamController<RemoteMessage>.broadcast();
  final StreamController<String?> _tokenController =
      StreamController<String?>.broadcast();

  /// Private constructor for singleton pattern
  NotificationService._();

  /// Get the singleton instance
  static NotificationService get instance {
    _instance ??= NotificationService._();
    return _instance!;
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Get the current FCM token
  String? get currentToken => _currentToken;

  /// Stream of incoming messages
  Stream<RemoteMessage> get messageStream => _messageController.stream;

  /// Stream of token updates
  Stream<String?> get tokenStream => _tokenController.stream;

  /// Initialize the notification service
  /// Must be called before using the service
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggingService.instance.logInfo(
        _logTag,
        'NotificationService already initialized',
      );
      return;
    }

    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Initializing NotificationService',
      );

      // Request permission for notifications
      await _requestPermission();

      // Get initial FCM token
      await _getToken();

      // Set up message handlers
      _setupMessageHandlers();

      // Listen for token refresh
      FirebaseMessaging.instance.onTokenRefresh.listen(_onTokenRefresh);

      _isInitialized = true;

      LoggingService.instance.logInfo(
        _logTag,
        'NotificationService initialized successfully',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to initialize NotificationService',
      );
      rethrow;
    }
  }

  /// Request notification permissions
  Future<NotificationSettings> _requestPermission() async {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Requesting notification permissions',
      );

      final settings = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      LoggingService.instance.logInfo(
        _logTag,
        'Permission status: ${settings.authorizationStatus}',
      );

      return settings;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to request notification permissions',
      );
      rethrow;
    }
  }

  /// Get FCM token and store it
  Future<void> _getToken() async {
    try {
      LoggingService.instance.logInfo(_logTag, 'Getting FCM token');

      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        _currentToken = token;
        _tokenController.add(token);

        LoggingService.instance.logInfo(
          _logTag,
          'FCM token obtained: ${token.substring(0, 20)}...',
        );

        // Store token in Firestore if user is logged in
        await _storeTokenInFirestore(token);
      } else {
        LoggingService.instance.logInfo(_logTag, 'FCM token is null');
      }
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to get FCM token',
      );
    }
  }

  /// Store FCM token in Firestore
  Future<void> _storeTokenInFirestore(String token) async {
    try {
      // Get current user ID from Firebase Auth
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'No authenticated user, skipping FCM token storage',
        );
        return;
      }

      LoggingService.instance.logInfo(
        _logTag,
        'Storing FCM token in Firestore: ${token.substring(0, 20)}...',
      );

      await FirestoreService.updateUserFCMToken(
        userId: currentUser.uid,
        fcmToken: token,
      );

      LoggingService.instance.logInfo(
        _logTag,
        'Successfully stored FCM token in Firestore',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to store FCM token in Firestore',
      );
    }
  }

  /// Handle token refresh
  void _onTokenRefresh(String token) {
    LoggingService.instance.logInfo(
      _logTag,
      'FCM token refreshed: ${token.substring(0, 20)}...',
    );

    _currentToken = token;
    _tokenController.add(token);

    // Store new token in Firestore
    _storeTokenInFirestore(token);
  }

  /// Set up message handlers for different app states
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen(_onForegroundMessage);

    // Handle messages when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen(_onMessageOpenedApp);

    // Handle messages when app is opened from terminated state
    _handleInitialMessage();
  }

  /// Handle messages when app is in foreground
  void _onForegroundMessage(RemoteMessage message) {
    LoggingService.instance.logInfo(
      _logTag,
      'Received foreground message: ${message.messageId}',
    );

    _messageController.add(message);

    // Show in-app notification or handle as needed
    _showInAppNotification(message);
  }

  /// Handle messages when app is opened from background
  void _onMessageOpenedApp(RemoteMessage message) {
    LoggingService.instance.logInfo(
      _logTag,
      'App opened from background message: ${message.messageId}',
    );

    _messageController.add(message);
    _handleMessageNavigation(message);
  }

  /// Handle initial message when app is opened from terminated state
  Future<void> _handleInitialMessage() async {
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      LoggingService.instance.logInfo(
        _logTag,
        'App opened from terminated state message: ${initialMessage.messageId}',
      );

      _messageController.add(initialMessage);
      _handleMessageNavigation(initialMessage);
    }
  }

  /// Show in-app notification for foreground messages
  void _showInAppNotification(RemoteMessage message) {
    // This will be implemented to show a custom in-app notification
    // For now, just log the message
    LoggingService.instance.logInfo(
      _logTag,
      'Would show in-app notification: ${message.notification?.title}',
    );
  }

  /// Handle navigation based on message data
  void _handleMessageNavigation(RemoteMessage message) {
    // This will be implemented to handle navigation based on message data
    // For now, just log the message
    LoggingService.instance.logInfo(
      _logTag,
      'Would handle navigation for message: ${message.data}',
    );
  }

  /// Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      LoggingService.instance.logInfo(_logTag, 'Subscribing to topic: $topic');

      await FirebaseMessaging.instance.subscribeToTopic(topic);

      LoggingService.instance.logInfo(
        _logTag,
        'Successfully subscribed to topic: $topic',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to subscribe to topic: $topic',
      );
      rethrow;
    }
  }

  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Unsubscribing from topic: $topic',
      );

      await FirebaseMessaging.instance.unsubscribeFromTopic(topic);

      LoggingService.instance.logInfo(
        _logTag,
        'Successfully unsubscribed from topic: $topic',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to unsubscribe from topic: $topic',
      );
      rethrow;
    }
  }

  /// Check current notification permission status
  Future<AuthorizationStatus> getPermissionStatus() async {
    try {
      final settings = await FirebaseMessaging.instance
          .getNotificationSettings();
      return settings.authorizationStatus;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to get permission status',
      );
      return AuthorizationStatus.notDetermined;
    }
  }

  /// Dispose of resources
  void dispose() {
    _messageController.close();
    _tokenController.close();
    _isInitialized = false;
  }
}
